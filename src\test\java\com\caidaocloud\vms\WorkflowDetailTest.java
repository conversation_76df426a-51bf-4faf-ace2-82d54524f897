package com.caidaocloud.vms;

import com.caidaocloud.vms.application.service.ProjectService;
import com.caidaocloud.vms.application.vo.WorkflowDetailVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 审批流程详情查询测试
 * 
 * <AUTHOR>
 * @date 2025/12/25
 */
@SpringBootTest
@ActiveProfiles("test")
public class WorkflowDetailTest {

    @Autowired
    private ProjectService projectService;

    @Test
    public void testLoadWorkflowDetail() {
        // 测试审批流程详情查询
        // 注意：这里需要一个真实的businessKey来测试
        // 格式为：hhistoryBid_VMS_PROJECT
        
        try {
            String businessKey = "h123456_VMS_PROJECT"; // 示例businessKey
            WorkflowDetailVO result = projectService.loadWorkflowDetail(businessKey);
            
            System.out.println("测试结果:");
            System.out.println("业务类型: " + result.getBusinessType());
            System.out.println("历史记录ID: " + result.getHistoryId());
            System.out.println("项目ID: " + result.getProjectId());
            System.out.println("岗位ID: " + result.getPositionId());
            
            if (result.getChanges() != null) {
                System.out.println("变更记录数量: " + result.getChanges().size());
            }
            
            if (result.getProjectDetail() != null) {
                System.out.println("项目详情: " + result.getProjectDetail().getProjectName());
            }
            
            if (result.getPositionDetail() != null) {
                System.out.println("岗位详情: " + result.getPositionDetail().getBid());
            }
            
            if (result.getSupplierDetail() != null) {
                System.out.println("供应商详情: " + result.getSupplierDetail().getSupplierName());
            }
            
            if (result.getContactDetail() != null) {
                System.out.println("联系人详情: " + result.getContactDetail().getBid());
            }
            
            if (result.getSettingDetail() != null) {
                System.out.println("设置详情: " + result.getSettingDetail().getBid());
            }
            
        } catch (Exception e) {
            System.out.println("测试异常（预期的，因为没有真实数据）: " + e.getMessage());
            // 这是预期的，因为测试环境中可能没有真实的审批记录
        }
    }
}
