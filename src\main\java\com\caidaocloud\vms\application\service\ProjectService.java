package com.caidaocloud.vms.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vms.application.dto.ProjectDto;
import com.caidaocloud.vms.application.dto.ProjectQueryDTO;
import com.caidaocloud.vms.application.vo.ProjectSettingVO;
import com.caidaocloud.vms.application.vo.ProjectSimpleVO;
import com.caidaocloud.vms.application.vo.ProjectVO;
import com.caidaocloud.vms.application.vo.WorkflowDetailVO;
import com.caidaocloud.vms.application.vo.ProjectPositionVO;
import com.caidaocloud.vms.application.vo.ProjectContactVO;
import com.caidaocloud.vms.application.vo.SupplierDetailVO;
import com.caidaocloud.vms.domain.project.annotation.HistoryRecord;
import com.caidaocloud.vms.domain.project.entity.ProjectSupplier;
import com.caidaocloud.vms.domain.project.entity.ProjectContact;
import com.caidaocloud.util.FastjsonUtil;
import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.vms.domain.project.entity.Project;
import com.caidaocloud.vms.domain.project.entity.ProjectHistory;
import com.caidaocloud.vms.domain.project.entity.ProjectPosition;
import com.caidaocloud.vms.domain.project.entity.ProjectSetting;
import com.caidaocloud.vms.domain.project.enums.HistoryType;
import com.caidaocloud.vms.domain.project.enums.OperationType;
import com.caidaocloud.vms.domain.project.enums.PositionStatus;
import com.caidaocloud.vms.domain.project.enums.ProjectStatus;
import com.caidaocloud.vms.domain.project.factory.ProjectFactory;
import com.caidaocloud.vms.domain.project.factory.ProjectHistoryFactory;
import com.caidaocloud.vms.domain.project.repository.ProjectPositionRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectRepository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.caidaocloud.vms.domain.project.entity.ProjectDraft;
import com.caidaocloud.vms.domain.project.entity.ProjectHistoryDetail;
import com.caidaocloud.vms.domain.project.repository.ProjectDraftRepository;
import com.caidaocloud.vms.domain.project.repository.ProjectSettingRepository;
import com.caidaocloud.vms.domain.base.service.WorkflowService;
import com.caidaocloud.vms.domain.base.exception.WorkflowStartException;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/6/3
 */
@Service
@Slf4j
public class ProjectService {
	@Autowired
	private ProjectRepository projectRepository;

	@Autowired
	private ProjectHistoryFactory projectHistoryFactory;
	@Autowired
	private ProjectHistoryService projectHistoryService;

	@Autowired
	private ProjectSettingRepository projectSettingRepository;

	@Autowired
	private ProjectDraftRepository projectDraftRepository;

	@Autowired
	private WorkflowService workflowService;

	@Autowired
	private ProjectPositionRepository projectPositionRepository;

	@Autowired
	private ProjectPositionService projectPositionService;

	@Autowired
	private ProjectContactService projectContactService;

	@Autowired
	private SupplierService supplierService;

	@Autowired
	private ProjectSettingService projectSettingService;

	/**
	 * 传入项目编码（必填）、项目名称（必填）、开始日期（时间戳），结束日期，预算总额，计划人员数量，项目负责人，所属公司，备注
	 * 保存项目基本信息，并初始化项目设置
	 *
	 * @return
	 */
	// @PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.CREATE)
	public String save(ProjectDto projectDto) {
		// 创建项目基本信息
		Project project = ProjectFactory.create(projectDto, workflowService.checkWorkflowEnable());

		// 保存项目
		projectRepository.saveOrUpdate(project);
		projectSettingRepository.init(project.getProjectSetting(), project.getBid());
		return project.getBid();
	}

	/**
	 * 编辑项目信息
	 * 
	 * @param projectDto 项目信息
	 */
	// @PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.UPDATE)
	public void edit(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}
		project.checkUpdate();
		// TODO: 2025/10/14 已用预算、实际人员

		// 更新项目信息
		project.updateBasicInfo(projectDto);
		// 保存项目
		projectRepository.saveOrUpdate(project);
	}

	/**
	 * 分页查询项目列表
	 * 
	 * @param queryDTO 查询条件
	 * @return 项目列表
	 */
	public PageResult<ProjectVO> projectPage(ProjectQueryDTO queryDTO) {
		// 执行分页查询
		PageResult<Project> projectPage = projectRepository.findByPage(queryDTO.getProjectName(), queryDTO);

		// 转换为VO列表
		List<ProjectVO> voList = projectPage.getItems().stream()
				.map(project -> {
					ProjectVO vo = ObjectConverter.convert(project, ProjectVO.class);
					// 设置状态枚举
					if (project.getStatus() != null) {
						vo.setStatus(ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue())));
					}
					return vo;
				})
				.collect(Collectors.toList());

		return new PageResult<>(voList, projectPage.getPageNo(), projectPage.getPageSize(), projectPage.getTotal());
	}

	/**
	 * 根据ID加载项目基础信息
	 * 
	 * @param projectId 项目ID
	 * @return 项目详细信息
	 */
	public ProjectVO loadProject(String projectId) {
		// 根据ID查询项目
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		// 转换为VO
		ProjectVO vo = ObjectConverter.convert(project, ProjectVO.class);

		// 设置状态枚举
		if (project.getStatus() != null) {
			vo.setStatus(ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue())));
		}

		return vo;
	}

	/**
	 * 删除项目
	 * 
	 */
	@PaasTransactional
	@HistoryRecord(dtoTypes = ProjectDto.class, historyType = HistoryType.BASIC_INFO, operationType = OperationType.DELETE)
	public void delete(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}

		// 删除项目
		projectRepository.delete(project);
	}

	/**
	 * 提交项目变更
	 * 根据draft生成change，保存historyDetail，发起工作流
	 * 无分布式事务，手动回滚
	 *
	 * @param projectId 项目ID
	 */
	public void commitProject(ProjectDto project) {
		SpringUtil.getBean(ProjectService.class).edit(project);
		doCommit(project.getBid());
	}

	private void doCommit(String projectId) {
		// 获取项目信息
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}

		// 获取项目相关的所有草稿
		Optional<ProjectDraft> optional = projectDraftRepository.getByTargetId(project.getBid());
		if (!optional.isPresent()) {
			log.info("No drafts found for project: " + projectId);
			return;
		}

		ProjectHistory projectHistory = null;
		try {
			ProjectHistoryDetail historyDetail = projectHistoryFactory.generateHistoryDetailFromDraft(project,
					optional.get());
			projectHistory = new ProjectHistory(projectId, historyDetail);
			projectHistoryService.saveHistory(projectHistory);

			workflowService.startWorkflow(projectHistory);

		} catch (WorkflowStartException e) {
			projectHistoryService.rollbackHistory(projectHistory);
			throw new ServerException("Failed to start workflow for project: " + projectId, e);
		} catch (Exception e) {
			throw new ServerException("Failed to commit project: " + projectId, e);
		}
	}

	public List<ProjectSimpleVO> loadProjectSelectList() {

		// 批量查询项目信息
		List<Project> projects = projectRepository.loadList();
		if (CollectionUtils.isEmpty(projects)) {
			return new ArrayList<>();
		}
		List<String> projectIds = Sequences.sequence(projects).map(AbstractData::getBid).toList();
		List<ProjectSetting> projectSettings = projectSettingRepository.loadList(projectIds);

		// 转换为VO
		return projects.stream()
				.map(project -> {
					ProjectSimpleVO vo = ObjectConverter.convert(project, ProjectSimpleVO.class);
					Option<ProjectSetting> settingOption = Sequences.sequence(projectSettings)
							.find(s -> project.getBid().equals(s.getProjectId()));
					if (settingOption.isDefined()) {
						vo.setSetting(ObjectConverter.convert(settingOption.get(), ProjectSettingVO.class));
					}
					return vo;
				})
				.collect(Collectors.toList());
	}

	public void start(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}
		ProjectStatus status = ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue()));
		if (status != ProjectStatus.APPROVED) {
			throw new ServerException("项目不可开始");
		}

		// 更新项目信息
		project.start();
		// 保存项目
		projectRepository.saveOrUpdate(project);
	}

	public void end(ProjectDto projectDto) {
		// 获取项目
		Project project = projectRepository.getById(projectDto.getBid());
		if (project == null) {
			throw new ServerException("Project not found: " + projectDto.getBid());
		}
		ProjectStatus status = ProjectStatus.fromCode(Integer.parseInt(project.getStatus().getValue()));
		if (status != ProjectStatus.IN_PROGRESS) {
			throw new ServerException("项目不可关闭");
		}

		// 更新项目信息
		project.end();
		// 保存项目
		projectRepository.saveOrUpdate(project);
	}

	public void refreshBudget(String projectId) {
		Project project = projectRepository.getById(projectId);
		if (project == null) {
			throw new ServerException("Project not found: " + projectId);
		}
		List<ProjectPosition> positionList = projectPositionRepository.loadPositionList(projectId);
		BigDecimal totalBudget = new BigDecimal(0);
		if (CollectionUtils.isEmpty(positionList)) {
			return;
		}
		for (ProjectPosition position : positionList) {
			if (position.getStatus() != PositionStatus.UN_SUBMITTED && position.getTotalBudget() != null) {
				totalBudget = totalBudget.add(BigDecimal.valueOf(position.getTotalBudget()));
			}
		}
		project.setTotalBudget(totalBudget);
		project.update();
		projectRepository.saveOrUpdate(project);
	}

	/**
	 * 根据businessKey加载审批流程详情
	 *
	 * @param businessKey 业务键，格式为hhistoryBid_VMS_PROJECT
	 * @return 审批流程详情信息
	 */
	public WorkflowDetailVO loadWorkflowDetail(String businessKey) {
		// 根据businessKey查询历史记录
		Optional<ProjectHistory> historyOpt = projectHistoryService.loadByBusinessKey(businessKey);
		if (!historyOpt.isPresent()) {
			throw new ServerException("审批记录不存在: " + businessKey);
		}

		ProjectHistory history = historyOpt.get();
		String historyId = history.getBid();
		String projectId = history.getProjectId();
		String positionId = history.getPositionId();

		// 查询历史详情记录
		List<ProjectHistoryDetail> detailList = projectHistoryService.loadDetail(historyId);

		WorkflowDetailVO result = new WorkflowDetailVO();
		result.setHistoryId(historyId);
		result.setProjectId(projectId);
		result.setPositionId(positionId);

		// 根据不同的业务类型查询对应的详情数据并组装change信息
		for (ProjectHistoryDetail detail : detailList) {
			HistoryType historyType = HistoryType.fromValue(detail.getType().getValue());
			result.setBusinessType(historyType.getCode());

			// 设置变更记录
			if (detail.getChange() != null) {
				result.setChanges(detail.getChange());
			}

			// 根据业务类型查询对应的详情数据
			switch (historyType) {
				case BASIC_INFO:
					// 查询项目基本信息
					if (projectId != null) {
						ProjectVO projectDetail = loadProject(projectId);
						result.setProjectDetail(projectDetail);
					}
					break;

				case POSITION:
					// 查询岗位信息
					if (positionId != null) {
						ProjectPositionVO positionDetail = projectPositionService.getPositionDetail(positionId);
						result.setPositionDetail(positionDetail);
					}
					break;

				case SUPPLIER:
					// 查询供应商信息 - 从snapshot中解析供应商ID
					if (detail.getSnapshot() != null) {
						try {
							List<ProjectSupplier> supplierList = FastjsonUtil.toObject(
									detail.getSnapshot(),
									new TypeReference<List<ProjectSupplier>>() {
									});
							if (!supplierList.isEmpty()) {
								// 取第一个供应商的ID查询详情
								String supplierId = supplierList.get(0).getSupplierId();
								if (supplierId != null) {
									SupplierDetailVO supplierDetail = supplierService.loadSupplier(supplierId);
									result.setSupplierDetail(supplierDetail);
								}
							}
						} catch (Exception e) {
							log.warn("解析供应商snapshot失败: {}", e.getMessage());
						}
					}
					break;

				case CONTACT:
					// 查询联系人信息 - 从snapshot中解析联系人ID
					if (detail.getSnapshot() != null) {
						try {
							List<ProjectContact> contactList = FastjsonUtil.toObject(
									detail.getSnapshot(),
									new TypeReference<List<ProjectContact>>() {
									});
							if (!contactList.isEmpty()) {
								// 取第一个联系人的ID查询详情
								String contactId = contactList.get(0).getBid();
								if (contactId != null && projectId != null) {
									// 通过项目ID查询联系人列表，然后找到对应的联系人
									List<ProjectContactVO> projectContacts = projectContactService
											.projectContactList(projectId);
									for (ProjectContactVO contact : projectContacts) {
										if (contactId.equals(contact.getBid())) {
											result.setContactDetail(contact);
											break;
										}
									}
								}
							}
						} catch (Exception e) {
							log.warn("解析联系人snapshot失败: {}", e.getMessage());
						}
					}
					break;

				case SETTING:
					// 查询设置信息
					if (projectId != null) {
						ProjectSettingVO settingDetail = projectSettingService.getByProjectId(projectId);
						result.setSettingDetail(settingDetail);
					}
					break;

				default:
					log.warn("未处理的业务类型: {}", historyType);
					break;
			}
		}

		return result;
	}
}
