package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.vms.application.service.WorkflowCallbackService;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作流回调Controller
 * 处理工作流审批结果的回调
 *
 * <AUTHOR>
 * @date 2025/10/17
 */
@RestController
@RequestMapping("/api/vms/v1/project/position/workflow")
@Api(tags = "工作流管理")
@Slf4j
public class PositionWorkflowController {

    @Autowired
    private WorkflowCallbackService workflowCallbackService;


    /**
     * 项目岗位工作流审批通过回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("/project/position/workflow/approve")
    @ApiOperation(value = "项目岗位工作流审批通过", notes = "项目岗位工作流审批通过后的回调处理")
    public Result approveProjectPositionWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("收到项目岗位工作流审批通过回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED);
         return Result.ok();

    }

    /**
     * 项目岗位工作流审批拒绝回调
     *
     * @param dto 业务单据ID（历史记录ID）
     * @return 操作结果
     */
    @PostMapping("reject")
    @ApiOperation(value = "项目岗位工作流审批拒绝", notes = "项目岗位工作流审批拒绝后的回调处理")
    public Result rejectProjectPositionWorkflow(
            @RequestBody WfCallbackResultDto dto) {
        
        log.info("收到项目岗位工作流审批拒绝回调，dto: {}", dto);
        workflowCallbackService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.REFUSED);
         return Result.ok();

    }


}
