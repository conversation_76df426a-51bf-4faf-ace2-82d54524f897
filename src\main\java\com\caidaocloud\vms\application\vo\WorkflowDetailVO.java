package com.caidaocloud.vms.application.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 审批流程项目详情VO
 * 
 * <AUTHOR>
 * @date 2025/12/25
 */
@Data
@ApiModel(description = "审批流程项目详情信息")
public class WorkflowDetailVO<T>  {
    
    @ApiModelProperty(value = "历史记录ID")
    private String historyId;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "变更记录列表")
    private List<ProjectChange> changes;
    
    @ApiModelProperty(value = "项目详细信息")
    private T detail;
}
