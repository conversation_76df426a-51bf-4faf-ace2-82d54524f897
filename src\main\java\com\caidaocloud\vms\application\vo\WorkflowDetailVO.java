package com.caidaocloud.vms.application.vo;

import com.caidaocloud.vms.domain.project.entity.ProjectChange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 审批流程详情VO
 * 
 * <AUTHOR>
 * @date 2025/12/25
 */
@Data
@ApiModel(description = "审批流程详情信息")
public class WorkflowDetailVO {

    @ApiModelProperty(value = "业务类型", example = "BASIC_INFO")
    private String businessType;

    @ApiModelProperty(value = "变更记录列表")
    private List<ProjectChange> changes;

    @ApiModelProperty(value = "项目基本信息详情")
    private ProjectVO projectDetail;

    @ApiModelProperty(value = "岗位信息详情")
    private ProjectPositionVO positionDetail;

    @ApiModelProperty(value = "供应商信息详情")
    private SupplierDetailVO supplierDetail;

    @ApiModelProperty(value = "联系人信息详情")
    private ProjectContactVO contactDetail;

    @ApiModelProperty(value = "设置信息详情")
    private ProjectSettingVO settingDetail;

    @ApiModelProperty(value = "历史记录ID")
    private String historyId;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "岗位ID")
    private String positionId;
}
