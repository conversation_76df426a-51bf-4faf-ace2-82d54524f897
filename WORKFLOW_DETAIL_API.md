# 审批流程详情查询接口

## 概述

新增了审批流程中的详情查询接口，用于查询项目的基本信息、岗位信息、供应商信息、联系人信息、设置信息。

## 接口信息

### 请求地址
```
GET /api/vms/v1/manager/project/workflow/detail
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| businessKey | String | 是 | 业务键，格式为hhistoryBid_VMS_PROJECT | h123456_VMS_PROJECT |

### 响应数据结构

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "businessType": "BASIC_INFO",
    "historyId": "123456",
    "projectId": "789012",
    "positionId": "345678",
    "changes": [
      {
        "fieldName": "项目名称",
        "oldValue": "旧项目名称",
        "newValue": "新项目名称",
        "changeType": "UPDATE"
      }
    ],
    "projectDetail": {
      "bid": "789012",
      "projectCode": "PRJ-2025-001",
      "projectName": "系统升级项目",
      "startDate": 1714521600000,
      "endDate": 1746057600000,
      "totalBudget": 1000000.00,
      "usedBudget": 500000.00,
      "plannedHeadcount": 10,
      "actualHeadcount": 8,
      "projectManager": {
        "empId": "EMP001",
        "name": "张三"
      },
      "company": "某某公司",
      "remarks": "备注信息",
      "status": "APPROVED"
    },
    "positionDetail": {
      "bid": "345678",
      "projectId": "789012",
      "position": "开发工程师",
      "plannedHeadcount": 5,
      "actualHeadcount": 3,
      "workplace": "北京",
      "employmentType": "FULL_TIME"
    },
    "supplierDetail": {
      "bid": "SUP001",
      "supplierCode": "SUP-001",
      "supplierName": "某某供应商",
      "phone": "010-12345678",
      "address": "北京市朝阳区"
    },
    "contactDetail": {
      "bid": "CON001",
      "projectId": "789012",
      "contact": {
        "empId": "EMP002",
        "name": "李四"
      },
      "email": "<EMAIL>",
      "phone": "***********",
      "organization": "技术部",
      "position": "技术经理"
    },
    "settingDetail": {
      "bid": "SET001",
      "projectId": "789012",
      "budgetEnabled": true,
      "quoteEnabled": false,
      "headcountEnabled": true,
      "positionAutoClose": false,
      "projectAutoClose": true,
      "positionApprovalFlow": true,
      "preHireEnabled": false
    }
  }
}
```

## 业务逻辑说明

### 1. businessKey解析
- 接口接收格式为 `hhistoryBid_VMS_PROJECT` 的businessKey
- 通过 `ProjectHistoryService.loadByBusinessKey()` 方法查询对应的历史记录

### 2. 详情数据查询
根据不同的业务类型（HistoryType），查询对应的详情数据：

- **BASIC_INFO**: 查询项目基本信息
- **POSITION**: 查询岗位信息
- **SUPPLIER**: 从snapshot中解析供应商ID并查询供应商详情
- **CONTACT**: 从snapshot中解析联系人ID并查询联系人详情
- **SETTING**: 查询项目设置信息

### 3. 变更记录组装
- 从 `ProjectHistoryDetail.change` 字段获取变更记录
- 变更记录包含字段名称、旧值、新值、变更类型等信息

## 使用示例

### JavaScript/Ajax调用
```javascript
$.ajax({
    url: '/api/vms/v1/manager/project/workflow/detail',
    type: 'GET',
    data: {
        businessKey: 'h123456_VMS_PROJECT'
    },
    success: function(response) {
        if (response.code === 200) {
            const detail = response.data;
            console.log('业务类型:', detail.businessType);
            console.log('项目详情:', detail.projectDetail);
            console.log('变更记录:', detail.changes);
        }
    },
    error: function(xhr, status, error) {
        console.error('请求失败:', error);
    }
});
```

### Java调用
```java
@Autowired
private ProjectService projectService;

public void getWorkflowDetail(String businessKey) {
    try {
        WorkflowDetailVO detail = projectService.loadWorkflowDetail(businessKey);
        System.out.println("业务类型: " + detail.getBusinessType());
        System.out.println("项目详情: " + detail.getProjectDetail());
        System.out.println("变更记录: " + detail.getChanges());
    } catch (Exception e) {
        log.error("查询审批流程详情失败", e);
    }
}
```

## 注意事项

1. **businessKey格式**: 必须严格按照 `hhistoryBid_VMS_PROJECT` 格式传入
2. **数据完整性**: 根据不同的业务类型，返回的详情字段可能为空
3. **异常处理**: 当businessKey对应的记录不存在时，会抛出 `ServerException`
4. **性能考虑**: 接口会根据业务类型查询对应的详情数据，避免不必要的数据查询

## 相关文件

- Controller: `ProjectController.java`
- Service: `ProjectService.java`
- VO: `WorkflowDetailVO.java`
- Test: `WorkflowDetailTest.java`
